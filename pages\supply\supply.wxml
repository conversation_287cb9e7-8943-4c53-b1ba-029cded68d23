<!-- pages/supply/supply.wxml --> 
<page-meta disable-scroll="{{showFilter}}"> 
  <navigation-bar title="供应中心" back="{{false}}" homeButton="{{true}}" extClass="custom-nav" hideTree="{{true}}"></navigation-bar> 
</page-meta> 
 
<!-- 防遮挡占位元素 --> 
<view class="nav-placeholder" style="height:{{topPadding}}px;"></view> 
 
<!-- 筛选面板 - 移到最外层 --> 
<view class="filter-panel {{showFilter ? 'show' : ''}}" catch:touchmove="preventTouchMove"> 
  <view class="filter-content" bindtouchstart="onFilterTouchStart" bindtouchmove="onFilterTouchMove"> 
    <view class="filter-header"> 
      <view class="filter-title">筛选条件</view> 
      <view class="filter-header-actions"> 
        <view class="header-reset-btn" hover-class="button-hover" bindtap="resetFilters">重置</view> 
        <view class="header-confirm-btn" hover-class="button-hover" bindtap="applyFilters">确定</view> 
      </view> 
    </view> 
    <!-- 高度区间 --> 
    <view class="filter-section"> 
      <view class="section-title" bindtap="toggleFilterSection" data-section="height"> 
        <text>高度区间</text> 
        <view class="section-controls"> 
          <view class="mode-switch" hover-class="mode-switch-hover" catchtap="toggleHeightMode"> 
            {{filters.isHeightExact ? '切换到区间' : '切换到精确'}} 
          </view> 
          <view class="toggle-icon"> 
            <t-icon name="{{visibleSections.height ? 'minus' : 'add'}}" size="20" color="#07c160"></t-icon> 
          </view> 
        </view> 
      </view> 
       
      <view class="section-content {{visibleSections.height ? '' : 'hidden'}}"> 
        <view class="range-input" wx:if="{{!filters.isHeightExact}}"> 
          <input  
            type="digit"  
            class="range-input-field"  
            placeholder="最小值"  
            value="{{filters.minHeight}}"  
            bindinput="onMinHeightChange" 
          /> 
          <view class="range-separator">~</view> 
          <input  
            type="digit"  
            class="range-input-field"  
            placeholder="最大值"  
            value="{{filters.maxHeight}}"  
            bindinput="onMaxHeightChange" 
          /> 
          <text class="unit">公分</text> 
        </view> 
         
        <view class="exact-input" wx:else> 
          <input  
            type="digit"  
            class="exact-input-field"  
            placeholder="高度"  
            value="{{filters.exactHeight}}"  
            bindinput="onExactHeightChange" 
          /> 
          <text class="unit">公分</text> 
        </view> 
      </view> 
    </view> 
     
    <!-- 树经区间 --> 
    <view class="filter-section"> 
      <view class="section-title" bindtap="toggleFilterSection" data-section="treeDiameter"> 
        <text>树经区间</text> 
        <view class="section-controls"> 
          <view class="mode-switch" hover-class="mode-switch-hover" catchtap="toggleTreeDiameterMode"> 
            {{filters.isTreeDiameterExact ? '切换到区间' : '切换到精确'}} 
          </view> 
          <view class="toggle-icon"> 
            <t-icon name="{{visibleSections.treeDiameter ? 'minus' : 'add'}}" size="20" color="#07c160"></t-icon> 
          </view> 
        </view> 
      </view> 
       
      <view class="section-content {{visibleSections.treeDiameter ? '' : 'hidden'}}"> 
        <view class="range-input" wx:if="{{!filters.isTreeDiameterExact}}"> 
          <input  
            type="digit"  
            class="range-input-field"  
            placeholder="最小值"  
            value="{{filters.minTreeDiameter}}"  
            bindinput="onMinTreeDiameterChange" 
          /> 
          <view class="range-separator">~</view> 
          <input  
            type="digit"  
            class="range-input-field"  
            placeholder="最大值"  
            value="{{filters.maxTreeDiameter}}"  
            bindinput="onMaxTreeDiameterChange" 
          /> 
          <text class="unit">公分</text> 
        </view> 
         
        <view class="exact-input" wx:else> 
          <input  
            type="digit"  
            class="exact-input-field"  
            placeholder="树经"  
            value="{{filters.exactTreeDiameter}}"  
            bindinput="onExactTreeDiameterChange" 
          /> 
          <text class="unit">公分</text> 
        </view> 
      </view> 
    </view> 
     
    <!-- 冠幅区间 --> 
    <view class="filter-section"> 
      <view class="section-title" bindtap="toggleFilterSection" data-section="canopy"> 
        <text>冠幅区间</text> 
        <view class="section-controls"> 
          <view class="mode-switch" hover-class="mode-switch-hover" catchtap="toggleCanopyMode"> 
            {{filters.isCanopyExact ? '切换到区间' : '切换到精确'}} 
          </view> 
          <view class="toggle-icon"> 
            <t-icon name="{{visibleSections.canopy ? 'minus' : 'add'}}" size="20" color="#07c160"></t-icon> 
          </view> 
        </view> 
      </view> 
       
      <view class="section-content {{visibleSections.canopy ? '' : 'hidden'}}"> 
        <view class="range-input" wx:if="{{!filters.isCanopyExact}}"> 
          <input  
            type="digit"  
            class="range-input-field"  
            placeholder="最小值"  
            value="{{filters.minCanopy}}"  
            bindinput="onMinCanopyChange" 
          /> 
          <view class="range-separator">~</view> 
          <input  
            type="digit"  
            class="range-input-field"  
            placeholder="最大值"  
            value="{{filters.maxCanopy}}"  
            bindinput="onMaxCanopyChange" 
          /> 
          <text class="unit">公分</text> 
        </view> 
         
        <view class="exact-input" wx:else> 
          <input  
            type="digit"  
            class="exact-input-field"  
            placeholder="冠幅"  
            value="{{filters.exactCanopy}}"  
            bindinput="onExactCanopyChange" 
          /> 
          <text class="unit">公分</text> 
        </view> 
      </view> 
    </view> 
     
    <!-- 杯口区间 --> 
    <view class="filter-section"> 
      <view class="section-title" bindtap="toggleFilterSection" data-section="cup"> 
        <text>杯口区间</text> 
        <view class="section-controls"> 
          <view class="mode-switch" hover-class="mode-switch-hover" catchtap="toggleCupMode"> 
            {{filters.isCupExact ? '切换到区间' : '切换到精确'}} 
          </view> 
          <view class="toggle-icon"> 
            <t-icon name="{{visibleSections.cup ? 'minus' : 'add'}}" size="20" color="#07c160"></t-icon> 
          </view> 
        </view> 
      </view> 
       
      <view class="section-content {{visibleSections.cup ? '' : 'hidden'}}"> 
        <view class="range-input" wx:if="{{!filters.isCupExact}}"> 
          <input  
            type="digit"  
            class="range-input-field"  
            placeholder="最小值"  
            value="{{filters.minCup}}"  
            bindinput="onMinCupChange" 
          /> 
          <view class="range-separator">~</view> 
          <input  
            type="digit"  
            class="range-input-field"  
            placeholder="最大值"  
            value="{{filters.maxCup}}"  
            bindinput="onMaxCupChange" 
          /> 
          <text class="unit">杯</text> 
        </view> 
         
        <view class="exact-input" wx:else> 
          <input  
            type="digit"  
            class="exact-input-field"  
            placeholder="杯口"  
            value="{{filters.exactCup}}"  
            bindinput="onExactCupChange" 
          /> 
          <text class="unit">杯</text> 
        </view> 
      </view> 
    </view> 
     
    <!-- 分支点区间 --> 
    <view class="filter-section"> 
      <view class="section-title" bindtap="toggleFilterSection" data-section="branchPos"> 
        <text>分支点区间</text> 
        <view class="section-controls"> 
          <view class="mode-switch" hover-class="mode-switch-hover" catchtap="toggleBranchPosMode"> 
            {{filters.isBranchPosExact ? '切换到区间' : '切换到精确'}} 
          </view> 
          <view class="toggle-icon"> 
            <t-icon name="{{visibleSections.branchPos ? 'minus' : 'add'}}" size="20" color="#07c160"></t-icon> 
          </view> 
        </view> 
      </view> 
       
      <view class="section-content {{visibleSections.branchPos ? '' : 'hidden'}}"> 
        <view class="range-input" wx:if="{{!filters.isBranchPosExact}}"> 
          <input  
            type="digit"  
            class="range-input-field"  
            placeholder="最小值"  
            value="{{filters.minBranchPos}}"  
            bindinput="onMinBranchPosChange" 
          /> 
          <view class="range-separator">~</view> 
          <input  
            type="digit"  
            class="range-input-field"  
            placeholder="最大值"  
            value="{{filters.maxBranchPos}}"  
            bindinput="onMaxBranchPosChange" 
          /> 
          <text class="unit">公分</text> 
        </view> 
         
        <view class="exact-input" wx:else> 
          <input  
            type="digit"  
            class="exact-input-field"  
            placeholder="分支点"  
            value="{{filters.exactBranchPos}}"  
            bindinput="onExactBranchPosChange" 
          /> 
          <text class="unit">公分</text> 
        </view> 
      </view> 
    </view> 
     
    <!-- 价格区间 --> 
    <view class="filter-section"> 
      <view class="section-title" bindtap="toggleFilterSection" data-section="price"> 
        <text>价格区间</text> 
        <view class="section-controls"> 
          <view class="mode-switch" hover-class="mode-switch-hover" catchtap="togglePriceMode"> 
            {{filters.isPriceExact ? '切换到区间' : '切换到精确'}} 
          </view> 
          <view class="toggle-icon"> 
            <t-icon name="{{visibleSections.price ? 'minus' : 'add'}}" size="20" color="#07c160"></t-icon> 
          </view> 
        </view> 
      </view> 
       
      <view class="section-content {{visibleSections.price ? '' : 'hidden'}}"> 
        <view class="range-input" wx:if="{{!filters.isPriceExact}}"> 
          <input  
            type="digit"  
            class="price-input"  
            placeholder="最低价"  
            value="{{filters.minPrice}}"  
            bindinput="onMinPriceChange" 
          /> 
          <view class="range-separator">~</view> 
          <input  
            type="digit"  
            class="price-input"  
            placeholder="最高价"  
            value="{{filters.maxPrice}}"  
            bindinput="onMaxPriceChange" 
          /> 
          <text class="unit">元</text> 
        </view> 
         
        <view class="exact-input" wx:else> 
          <input  
            type="digit"  
            class="exact-input-field"  
            placeholder="价格"  
            value="{{filters.exactPrice}}"  
            bindinput="onExactPriceChange" 
          /> 
          <text class="unit">元</text> 
        </view> 
      </view> 
    </view> 
 
    <!-- 分支点筛选部分之后 --> 
    <view class="filter-section"> 
      <view class="section-title" bindtap="toggleFilterSection" data-section="clumpCount"> 
        <text>丛生数量区间</text> 
        <view class="section-controls"> 
          <view class="mode-switch" hover-class="mode-switch-hover" catchtap="toggleClumpCountMode"> 
            {{filters.isClumpCountExact ? '切换到区间' : '切换到精确'}} 
          </view> 
          <view class="toggle-icon"> 
            <t-icon name="{{visibleSections.clumpCount ? 'minus' : 'add'}}" size="20" color="#07c160"></t-icon> 
          </view> 
        </view> 
      </view> 
       
      <view class="section-content {{visibleSections.clumpCount ? '' : 'hidden'}}"> 
        <view class="range-input" wx:if="{{!filters.isClumpCountExact}}"> 
          <input  
            type="digit"  
            class="range-input-field"  
            placeholder="最小值"  
            value="{{filters.minClumpCount}}"  
            bindinput="onMinClumpCountChange" 
          /> 
          <view class="range-separator">~</view> 
          <input  
            type="digit"  
            class="range-input-field"  
            placeholder="最大值"  
            value="{{filters.maxClumpCount}}"  
            bindinput="onMaxClumpCountChange" 
          /> 
          <text class="unit">杆</text> 
        </view> 
         
        <view class="exact-input" wx:else> 
          <input  
            type="digit"  
            class="exact-input-field"  
            placeholder="丛生数量"  
            value="{{filters.exactClumpCount}}"  
            bindinput="onExactClumpCountChange" 
          /> 
          <text class="unit">杆</text> 
        </view> 
      </view> 
    </view> 
 
    <!-- 杆径筛选 --> 
    <view class="filter-section"> 
      <view class="section-title" bindtap="toggleFilterSection" data-section="clumpDiameter"> 
        <text>杆径区间</text> 
        <view class="section-controls"> 
          <view class="mode-switch" hover-class="mode-switch-hover" catchtap="toggleClumpDiameterMode"> 
            {{filters.isClumpDiameterExact ? '切换到区间' : '切换到精确'}} 
          </view> 
          <view class="toggle-icon"> 
            <t-icon name="{{visibleSections.clumpDiameter ? 'minus' : 'add'}}" size="20" color="#07c160"></t-icon> 
          </view> 
        </view> 
      </view> 
       
      <view class="section-content {{visibleSections.clumpDiameter ? '' : 'hidden'}}"> 
        <view class="range-input" wx:if="{{!filters.isClumpDiameterExact}}"> 
          <input  
            type="digit"  
            class="range-input-field"  
            placeholder="最小值"  
            value="{{filters.minClumpDiameter}}"  
            bindinput="onMinClumpDiameterChange" 
          /> 
          <view class="range-separator">~</view> 
          <input  
            type="digit"  
            class="range-input-field"  
            placeholder="最大值"  
            value="{{filters.maxClumpDiameter}}"  
            bindinput="onMaxClumpDiameterChange" 
          /> 
          <text class="unit">公分</text> 
        </view> 
         
        <view class="exact-input" wx:else> 
          <input  
            type="digit"  
            class="exact-input-field"  
            placeholder="杆径"  
            value="{{filters.exactClumpDiameter}}"  
            bindinput="onExactClumpDiameterChange" 
          /> 
          <text class="unit">公分</text> 
        </view> 
      </view> 
    </view> 
 
    <!-- 分类筛选 - 添加到质量筛选前面 --> 
    <view class="filter-section"> 
      <!-- <view class="section-title" bindtap="toggleFilterSection" data-section="category"> 
        <text>分类筛选</text> 
        <view class="toggle-icon"> 
          <t-icon name="{{visibleSections.category ? 'minus' : 'add'}}" size="20" color="#07c160"></t-icon> 
        </view> 
      </view>  -->
       
      <view class="section-content {{visibleSections.category ? '' : 'hidden'}}"> 
        <view class="category-options"> 
          <view  
            class="category-option {{filters.category === 'all' ? 'active' : ''}}"  
            data-category="all"  
            bindtap="onCategoryChange" 
          >全部</view> 
          <view  
            class="category-option {{filters.category === '乔木' ? 'active' : ''}}"  
            data-category="乔木"  
            bindtap="onCategoryChange" 
          >乔木</view> 
          <view  
            class="category-option {{filters.category === '灌木' ? 'active' : ''}}"  
            data-category="灌木"  
            bindtap="onCategoryChange" 
          >灌木</view> 
          <view  
            class="category-option {{filters.category === '藤本类' ? 'active' : ''}}"  
            data-category="藤本类"  
            bindtap="onCategoryChange" 
          >藤本类</view> 
          <view  
            class="category-option {{filters.category === '草皮类' ? 'active' : ''}}"  
            data-category="草皮类"  
            bindtap="onCategoryChange" 
          >草皮类</view> 
          <view  
            class="category-option {{filters.category === '花草' ? 'active' : ''}}"  
            data-category="花草"  
            bindtap="onCategoryChange" 
          >花草</view> 
          <view  
            class="category-option {{filters.category === '种子' ? 'active' : ''}}"  
            data-category="种子"  
            bindtap="onCategoryChange" 
          >种子</view> 
        </view> 
      </view> 
    </view> 
     
    <!-- 质量筛选 --> 
    <view class="filter-section"> 
      <view class="section-title" bindtap="toggleFilterSection" data-section="quality"> 
        <text>质量筛选</text> 
        <view class="toggle-icon"> 
          <t-icon name="{{visibleSections.quality ? 'minus' : 'add'}}" size="20" color="#07c160"></t-icon> 
        </view> 
      </view> 
       
      <view class="section-content {{visibleSections.quality ? '' : 'hidden'}}"> 
        <view class="quality-options"> 
          <view  
            class="quality-option {{filters.quality === 'all' ? 'active' : ''}}"  
            data-quality="all"  
            bindtap="onQualityChange" 
          >全部</view> 
          <view  
            class="quality-option {{filters.quality === '精品' ? 'active' : ''}}"  
            data-quality="精品"  
            bindtap="onQualityChange" 
          >精品</view> 
          <view  
            class="quality-option {{filters.quality === '中等' ? 'active' : ''}}"  
            data-quality="中等"  
            bindtap="onQualityChange" 
          >中等</view> 
          <view  
            class="quality-option {{filters.quality === '处理' ? 'active' : ''}}"  
            data-quality="处理"  
            bindtap="onQualityChange" 
          >处理</view> 
        </view> 
      </view> 
    </view> 
  </view> 
</view> 
 
<!-- 筛选遮罩层 --> 
<view class="filter-mask {{showFilter ? 'show' : ''}}" bindtap="closeFilter" catch:touchmove="preventTouchMove"></view> 
 
<!-- 主容器 --> 
<view class="container {{showFilter ? 'showFilter' : ''}}"> 
  <!-- 搜索栏和标签栏容器 - 固定定位在顶部 --> 
  <view class="top-container" style="top:{{topPadding}}px; height:auto;"> 
    <!-- 搜索栏 --> 
    <view class="search-bar"> 
      <view class="search-box"> 
        <icon type="search" size="16" color="#07c160"></icon> 
        <input  
          class="search-input"  
          placeholder="搜索你需要植株"  
          placeholder-class="search-placeholder" 
          value="{{searchValue}}" 
          bindinput="onSearchChange" 
          bindconfirm="onSearch" 
          bindfocus="onSearchFocus" 
          bindblur="onSearchBlur" 
        /> 
        <view wx:if="{{searchValue}}" class="search-clear" catchtap="clearSearch"> 
          <icon type="clear" size="14" color="#999"></icon> 
        </view> 
      </view> 
      <view class="search-btn" bindtap="onSearch">搜索</view> 
    </view> 
 
    <!-- 排序栏 - 使用TDesign Tabs组件 -->
    <view class="sort-bar-container">
      <t-tabs
        value="{{sortType}}"
        bind:change="onTabsChange"
        theme="line"
        placement="top"
        swipeable="{{true}}"
        sticky="{{false}}"
        ext-class="custom-tabs"
      >
        <t-tab-panel
          wx:for="{{sortTabs}}"
          wx:key="value"
          label="{{item.label}}"
          value="{{item.value}}"
        />
      </t-tabs>

      <!-- 筛选按钮 -->
      <view class="filter-btn {{showFilter ? 'active' : ''}}" bindtap="toggleFilter">
        <text>更多筛选</text>
        <t-icon name="filter" size="16" color="{{showFilter ? '#fff' : '#666'}}"></t-icon>
      </view>
    </view>
     
  </view> 
   
  <!-- 供应列表容器 - 使用contentPadding来避免被顶部遮挡 --> 
  <view class="supply-content" style="padding-top:{{contentPadding}}px;">
    
    <!-- 新增的精确筛选容器 - 移到supply-content内部，可以随内容滚动 --> 
    <view class="quick-filter-bar"> 
      <view class="quick-filter-grid"> 
 
        <view class="quick-filter-item"> 
          <text class="quick-filter-label">树经</text> 
          <input class="quick-filter-input" type="digit" placeholder="" value="{{filters.exactTreeDiameter}}" bindinput="onQuickTreeDiameterChange" /> 
          <text class="quick-filter-unit">公分</text> 
        </view> 
 
 
        <view class="quick-filter-item"> 
          <text class="quick-filter-label">高度</text> 
          <input class="quick-filter-input" type="digit" placeholder="" value="{{filters.exactHeight}}" bindinput="onQuickHeightChange" /> 
          <text class="quick-filter-unit">公分</text> 
        </view> 
         
       
         
        <view class="quick-filter-item"> 
          <text class="quick-filter-label">冠幅</text> 
          <input class="quick-filter-input" type="digit" placeholder="" value="{{filters.exactCanopy}}" bindinput="onQuickCanopyChange" /> 
          <text class="quick-filter-unit">公分</text> 
        </view> 
         
        <view class="quick-filter-item" style="margin-left: -9%;"> 
          <text class="quick-filter-label">丛生</text> 
          <input class="quick-filter-input" type="digit" placeholder="" value="{{filters.exactClumpCount}}" bindinput="onQuickClumpCountChange" /> 
          <text class="quick-filter-unit">杆</text> 
        </view> 
         
        <view class="quick-filter-item"> 
          <text class="quick-filter-label">杆径</text> 
          <input class="quick-filter-input" type="digit" placeholder="" value="{{filters.exactClumpDiameter}}" bindinput="onQuickClumpDiameterChange" /> 
          <text class="quick-filter-unit">公分</text> 
        </view> 
         
        <view class="quick-filter-item"> 
          <text class="quick-filter-label">分支</text> 
          <input class="quick-filter-input" type="digit" placeholder="" value="{{filters.exactBranchPos}}" bindinput="onQuickBranchPosChange" /> 
          <text class="quick-filter-unit">公分</text> 
        </view> 
      </view> 
    </view> 
    <!-- 植物名称推荐列表 --> 
    <view class="plant-suggestions-container" wx:if="{{showSuggestions && plantSuggestions.length > 0}}" catchtap="stopPropagation" catchtouchmove="stopPropagation" catchlongpress="stopPropagation"> 
      <view class="suggestions-header"> 
        <text class="suggestions-title">你想搜索 (共{{plantSuggestions.length}}项)</text> 
        <view class="suggestions-close" bindtap="closeSuggestions">×</view> 
      </view> 
      <scroll-view scroll-y class="suggestions-scroll" catchtouchmove="stopPropagation"> 
        <view class="suggestions-scroll-inner">
          <view class="suggestion-item" wx:for="{{plantSuggestions}}" wx:key="index" hover-class="suggestion-item-hover" catchtap="onSelectSuggestion" data-name="{{item.name}}"> 
            <text class="suggestion-name">{{item.name}}</text> 
          </view> 
        </view>
      </scroll-view> 
    </view> 
     
    <!-- 供应列表 --> 
    <view class="supply-list"> 
      <!-- 有数据时显示列表 --> 
      <view class="supply-list-container"> 
        <block wx:if="{{supplyList.length > 0}}"> 
          <view  
            wx:for="{{supplyList}}"  
            wx:key="id"  
            class="supply-item"  
            bindtap="onSupplyItemTap" 
            data-id="{{item.id}}" 
          > 
            <!-- 供应项整体采用flex布局 --> 
            <view class="supply-item-container"> 
              <!-- 标题卡片与数量一体化 --> 
              <view class="title-section"> 
                <view class="title-card"> 
                  <view class="title-text"> 
                    <!-- <text class="supply-prefix">{{item.titlePrefix}}</text>  -->
                    <text class="supply-content" style="{{item.titleContent.length >= 6 ? 'font-size: 30rpx;' : ''}}">{{item.titleContent}}</text> 
                  </view> 
                   
                  <!-- 移动价格到标题卡片右侧 --> 
                  <view class="title-price-info"> 
                    <!-- 上车价，只在有值时显示 --> 
                    <view class="title-price-item" wx:if="{{item.price}}"> 
                      <text class="price-label">上车价:</text>  
                      <text class="price-value">¥{{item.price}}</text> 
                    </view> 
                     
                    <!-- 地价，只在有值时显示 --> 
                    <view class="title-price-item" wx:if="{{item.rawData && item.rawData.land_price}}"> 
                      <text class="price-label">地价:</text>  
                      <text class="price-value">¥{{item.rawData.land_price}}</text> 
                    </view> 
                     
                    <!-- 如果两个价格都没有，显示询价 --> 
                    <view class="title-price-item" wx:if="{{!item.price && (!item.rawData || !item.rawData.land_price)}}"> 
                      <text class="price-label">价格</text> 
                      <text class="price-value">询价</text> 
                    </view> 
                  </view> 
                </view> 
              </view> 
               
 
            </view> 
             
            <!-- 主要内容区域 --> 
            <view class="supply-item-content"> 
              <!-- 图片和内容布局 --> 
              <view class="image-content-container"> 
                <!-- 预览图 --> 
                <image  
                  wx:if="{{item.images.length > 0}}" 
                  src="{{item.images[0]}}"  
                  mode="aspectFill" 
                  class="preview-image" 
                  style="position: relative; left: -20rpx; top: 0rpx"></image> 
                 
                <view class="content-specs-container"> 
                  <!-- 内容区域已移至title-section下方 --> 
                   
                  <!-- 规格信息 --> 
                  <view class="specs-info" wx:if="{{item.images.length > 0}}"> 

                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.meter_diameter}}"> 
                      <text class="spec-label">米径:</text> 
                      <text class="spec-value quality-value">{{item.rawData.meter_diameter}}公分</text> 
                    </view> 

                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.height}}"> 
                      <text class="spec-label">高度:</text> 
                      <text class="spec-value">{{item.rawData.height}}公分</text> 
                    </view> 
                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.canopy}}"> 
                      <text class="spec-label">冠幅:</text> 
                      <text class="spec-value">{{item.rawData.canopy}}公分</text> 
                    </view> 

              
                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.ground_diameter}}"> 
                      <text class="spec-label">地径:</text> 
                      <text class="spec-value">{{item.rawData.ground_diameter}}公分</text> 
                    </view> 
                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.thorax_diameter}}"> 
                      <text class="spec-label">胸径:</text> 
                      <text class="spec-value">{{item.rawData.thorax_diameter}}公分</text> 
                    </view> 
                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.branchPos}}"> 
                      <text class="spec-label">分枝:</text> 
                      <text class="spec-value">{{item.rawData.branchPos}}公分</text> 
                    </view> 
                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.cup}}"> 
                      <text class="spec-label">杯口:</text> 
                      <text class="spec-value">{{item.rawData.cup}}杯</text> 
                    </view> 
                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.main_vine_length}}"> 
                      <text class="spec-label">主蔓长度:</text> 
                      <text class="spec-value">{{item.rawData.main_vine_length}}公分</text> 
                    </view> 
                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.branch_count}}"> 
                      <text class="spec-label">分支数:</text> 
                      <text class="spec-value">{{item.rawData.branch_count}}支</text> 
                    </view> 
                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.plant_density}}"> 
                      <text class="spec-label">密度:</text> 
                      <text class="spec-value">{{item.rawData.plant_density}}株/m²</text> 
                    </view> 
                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.clumpCount}}"> 
                      <text class="spec-label">丛生:</text> 
                      <text class="spec-value">{{item.rawData.clumpCount}}杆</text> 
                    </view> 
                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.clumpDiameter}}"> 
                      <text class="spec-label">杆径:</text> 
                      <text class="spec-value">{{item.rawData.clumpDiameter}}公分</text> 
                    </view> 
                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.plant_age}}"> 
                      <text class="spec-label">苗龄:</text> 
                      <text class="spec-value">{{item.rawData.plant_age}}</text> 
                    </view> 
                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.plant_method && item.rawData.plant_method !== '无'}}"> 
                      <text class="spec-label">栽培状态:</text> 
                      <text class="spec-value">{{item.rawData.plant_method}}</text> 
                    </view> 
                    <!-- 数量信息移动到规格信息的最后一项之前 -->
                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.quantity}}">
                      <text class="spec-label">数量:</text>
                      <text class="spec-value">{{item.rawData.quantity}}{{item.rawData.price_unit || item.rawData.unit || ''}}</text>
                    </view>
                    <!-- 质量信息移到最后，并使用特殊样式 --> 
                    <view class="spec-item" wx:if="{{item.rawData && item.rawData.quality}}"> 
                      <text class="spec-label">质量:</text> 
                      <text class="spec-value quality-value">{{item.rawData.quality}}</text> 
                    </view>
                    
                    <!-- 简介内容移到specs-info的最底部 --> 
                    <view class="content-container" wx:if="{{item.content}}"> 
                      <view class="content-wrapper"> 
                        <text class="content-label">简介:</text> 
                        <text class="content">{{item.content}}</text> 
                      </view> 
                    </view> 
                  </view> 
                </view> 
              </view> 
            </view> 
             
                        <!-- 底部价格和操作区域 -->
            <view class="supply-item-footer">
              <!-- 用户信息和标签部分移动到这里 -->
              <view class="user-tag-container">
                <!-- 左侧发布时间 -->
                <view class="supply-item-left">
                  <view class="time-info">
                    <view class="publish-time">{{item.timeAgo}}</view>
                  </view>
                </view>
                
                <!-- 右侧标签信息 -->
                <view class="supply-item-right">
                  <view class="category">{{item.category}}</view>
                  <view class="distance" wx:if="{{item.calculatedDistance}}">{{item.calculatedDistance}}</view>
                </view>
              </view>
            </view>
            
            <!-- 退林还耕单独一行容器 -->
            <view class="return-container" wx:if="{{item.rawData && item.rawData.return === true}}">
              <view class="return-tag-wrapper">
                <view class="return-tag">退林还耕</view>
              </view>
            </view> 
 
          </view> 
        </block> 
         
        <!-- 空状态 --> 
        <view wx:if="{{!loading && supplyList.length === 0}}" class="empty-state"> 
          <t-icon name="info-circle" size="80rpx" color="#07c160"></t-icon> 
          <text class="empty-text">暂无供应信息</text> 
        </view> 
         
        <!-- 加载状态 --> 
        <view wx:if="{{loading}}" class="loading"> 
          <view class="loading-spinner"></view> 
          <text class="loading-text">加载中...</text> 
        </view> 
         
        <!-- 加载更多/无更多数据 --> 
        <view wx:if="{{!loading && hasMore && supplyList.length > 0}}" class="load-more" bindtap="loadMoreData"> 
          点击加载更多 
        </view> 
        <view wx:elif="{{!loading && !hasMore && supplyList.length > 0}}" class="no-more"> 
          没有更多数据了 
        </view> 
      </view> 
    </view> 
  </view> 
 
  <!-- 悬浮按钮 --> 
  <!-- <view class="publish-btn" bindtap="onPublishTap" wx:if="{{!showFilter}}"> 
    <t-icon name="add" size="28" color="#fff"></t-icon> 
    <text>发布供应</text> 
  </view>  -->
 
  <!-- 回到顶部 --> 
  <view class="back-top" wx:if="{{scrollTop > 300 && !showFilter}}" bindtap="onBackTop"> 
    <t-icon name="chevron-up" size="20" color="#07c160"></t-icon> 
  </view> 
   
  <!-- 自定义tabbar --> 
  <custom-tab-bar></custom-tab-bar> 
</view> 
 \ No newline at end of file 