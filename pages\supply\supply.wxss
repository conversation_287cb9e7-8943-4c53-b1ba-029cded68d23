/* pages/supply/supply.wxss */
page {
  background-color: #f4f9f4; /* 更柔和的浅绿色背景，与home页面一致 */
  position: relative;
  min-height: 100vh;
  overflow: auto; /* 修改为auto，允许页面滚动 */
  height: 100%;
  box-sizing: border-box;
}

/* 容器采用弹性布局 */
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  position: relative;
  background-color: transparent;
}

/* 导航栏占位元素 */
.nav-placeholder {
  width: 100%;
  background: #43a047; /* 与导航栏背景色一致 */
  position: fixed;
  top: 0;
  left: 0;
  z-index: 998; /* 在导航栏下方，顶部容器上方 */
}

/* 自定义导航栏样式 */
.custom-nav {
  background: #43a047 !important; /* 纯色背景 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1) !important;
  z-index: 1000 !important; /* 确保最高层级 */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
}

.custom-nav::after {
  display: none;
}

.custom-nav .weui-navigation-bar__inner {
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

.custom-nav .weui-navigation-bar__center {
  color: #ffffff !important; /* 白色文字 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
  font-weight: bold;
}



/* 供应内容区域 - 新增 */
.supply-content {
  
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
}

/* 供应列表 */
.supply-list {
  width: 100%;
  box-sizing: border-box;
  padding-bottom: 120rpx; /* 底部添加足够的内边距，避免被TabBar遮挡 */
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 20rpx;
  background-color: #fff;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 28rpx;
  padding: 6rpx 20rpx;
  flex: 1;
  margin-right: 15rpx;
}

/* 退林还耕筛选开关样式 */
.forest-return-filter {
  display: flex;
  align-items: center;
  margin: 0 10rpx;
  white-space: nowrap;
  background: linear-gradient(to right, rgba(255, 87, 34, 0.05), rgba(255, 87, 34, 0.1));
  padding: 2rpx 12rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 87, 34, 0.2);
}

.forest-return-filter text {
  font-size: 24rpx;
  color: #e53935;
  margin-right: 6rpx;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(229, 57, 53, 0.2);
}

.forest-return-switch {
  transform: scale(0.7);
  margin-right: -6rpx;
}

.search-input {
  flex: 1;
  height: 56rpx;
  font-size: 28rpx;
  margin-left: 15rpx;
}



/* 快速筛选栏样式 - 调整内边距 */
.quick-filter-bar {
  background-color: #ffffff;
  padding: 10rpx 24rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

/* 筛选面板和遮罩层样式 */
.filter-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}

/* 响应式调整 */
@media screen and (min-width: 768px) {
  .top-container {
    padding-left: 5%;
    padding-right: 5%;
  }
  
  .supply-list {
    padding-left: 5%;
    padding-right: 5%;
  }
}

/*margin-top为和上面 筛选栏的距离*/
.container {
  padding: 0;
  margin-top: 0; /* 重置原来的负边距 */
  padding-top: 220rpx; /* 给顶部容器预留足够空间 */
  background-color: transparent;
  display: flex;
  flex-direction: column;
  overflow-x: hidden; /* 禁止水平滚动 */
  width: 100%;
  box-sizing: border-box;
}

/* 自定义导航栏样式 */
.custom-nav {
  background: linear-gradient(to bottom, #43a047, #43a047) !important; /* 改为纯色，不使用透明度 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1) !important;
  z-index: 1000 !important; /* 提高z-index确保在最上层 */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
}

.custom-nav::after {
  display: none;
}

.custom-nav .weui-navigation-bar__inner {
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

.custom-nav .weui-navigation-bar__center {
  color: #ffffff !important; /* 白色文字 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
  font-weight: bold;
}

.custom-nav .weui-navigation-bar__btn_goback_wrapper {
  transition: all 0.3s ease;
}

.custom-nav .weui-navigation-bar__btn_goback_wrapper.weui-active {
  opacity: 0.7;
  transform: scale(0.95);
}

/* 顶部容器 */
.top-container {
  position: fixed; /* 固定定位 */
  left: 0;
  right: 0;
  z-index: 999; /* 确保在导航栏之下，但在其他内容之上 */
  background: linear-gradient(to bottom, #43a047, #e8f5e9); /* 使用渐变但不透明 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  padding-top: 10rpx; /* 顶部添加一点内边距 */
  border-bottom: 1rpx solid rgba(76, 175, 80, 0.3); /* 添加边框增强分隔效果 */
  display: flex;
  flex-direction: column;
}

/* 添加连接元素样式 */
.top-container::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #e8f5e9;
  z-index: 2;
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  margin-top: 6rpx; /* 增加一点上边距 */
  background-color: #ffffff; /* 改为纯白色背景 */
  position: relative;
  overflow: hidden;
}

.search-box {
  display: flex;
  align-items: center;
  flex: 1;
  height: 72rpx;
  background-color: #e8f5e9; /* 使用纯色背景，移除渐变 */
  border-radius: 36rpx;
  padding: 0 20rpx;
  border: 1rpx solid rgba(76, 175, 80, 0.25);
  box-shadow: 0 4rpx 15rpx rgba(56, 142, 60, 0.15);
  transition: all 0.3s ease;
}

.search-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 80% 20%, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.1) 5%, transparent 10%),
    radial-gradient(circle at 20% 80%, rgba(76, 175, 80, 0.08) 0%, rgba(76, 175, 80, 0.08) 7%, transparent 14%);
  background-size: 200rpx 200rpx, 250rpx 250rpx;
  opacity: 0.8;
  z-index: -1;
}

.search-box:active {
  background-color: #f2f2f2;
  transform: scale(0.98);
}

.search-input {
  flex: 1;
  height: 72rpx;
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #333;
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
}

.search-clear {
  padding: 10rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 10; /* 确保清除按钮在最上层 */
}

/* 增大清除按钮的点击区域 */
.search-clear icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.search-btn {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #ffffff;
  background: linear-gradient(135deg, #43a047, #2e7d32);
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 8rpx rgba(46, 125, 50, 0.2);
  transition: all 0.3s ease;
}

.search-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(46, 125, 50, 0.1);
}

/* 标签栏样式 */
.tabs-scroll {
  width: 100%;
  white-space: nowrap;
  background: #ffffff; /* 改为纯白色背景 */
  border-bottom: 1rpx solid rgba(76, 175, 80, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tabs {
  display: flex;
  padding: 0 12rpx;
  background-color: transparent;
  position: relative;
  overflow: hidden;
}

.tabs::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 30% 50%, rgba(76, 175, 80, 0.08) 0%, rgba(76, 175, 80, 0.08) 2%, transparent 4%),
    radial-gradient(circle at 70% 40%, rgba(76, 175, 80, 0.06) 0%, rgba(76, 175, 80, 0.06) 1%, transparent 2%);
  background-size: 150rpx 150rpx, 120rpx 120rpx;
  opacity: 0.5;
  z-index: -1;
}

.tab-item {
  position: relative;
  padding: 24rpx 30rpx 20rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  display: inline-block;
  transition: all 0.3s;
}

.tab-item.active {
  color: #07c160;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #07c160;
  border-radius: 6rpx;
  box-shadow: 0 2rpx 4rpx rgba(7, 193, 96, 0.2);
}

/* 排序栏样式 */
.sort-bar {
  width: 100%;
  white-space: nowrap;
  padding: 12rpx 0;
  background: #e8f5e9; /* 改为纯色背景，不使用透明度 */
  border-top: 1rpx solid rgba(76, 175, 80, 0.2);
  border-bottom: 1rpx solid rgba(76, 175, 80, 0.2);
  position: relative;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

/* 隐藏WebKit浏览器的滚动条 */
.sort-bar::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  background: transparent;
}

/* 确保在所有平台上隐藏滚动条 */
.sort-bar::-webkit-scrollbar-thumb {
  background: transparent;
  width: 0;
  height: 0;
}

.sort-bar::-webkit-scrollbar-track {
  background: transparent;
  width: 0;
  height: 0;
}

/* 添加额外的滚动条隐藏样式，适用于更多平台 */
.sort-bar::-webkit-scrollbar-button {
  display: none;
  width: 0;
  height: 0;
}

.sort-bar::-webkit-scrollbar-corner {
  background: transparent;
}

/* 修改内部容器，确保内容不会触发滚动条显示 */
.sort-bar-inner {
  display: inline-flex;
  padding: 0 24rpx;
  min-width: 100%;
  box-sizing: border-box;
}

.sort-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 80% 20%, rgba(76, 175, 80, 0.15) 0%, rgba(76, 175, 80, 0.15) 5%, transparent 10%),
    radial-gradient(circle at 20% 80%, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.1) 7%, transparent 14%);
  background-size: 200rpx 200rpx, 250rpx 250rpx;
  opacity: 0.6;
  z-index: 0;
}

.sort-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0rpx 10rpx;
  margin-right: 12rpx;
  font-size: 25rpx;
  color: #666;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 30rpx;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  border: 1rpx solid rgba(76, 175, 80, 0.15);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.sort-item.active {
  color: #fff;
  background: linear-gradient(135deg, #43a047, #2e7d32);
  font-weight: 500;
  box-shadow: 0 4rpx 8rpx rgba(46, 125, 50, 0.2);
  border: none;
}

.sort-item:active {
  opacity: 0.8;
  transform: scale(0.95);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

/* 筛选按钮 */
.filter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 30rpx;
  font-size: 26rpx;
  color: #666;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 30rpx;
  transition: all 0.3s ease;
  margin-left: auto;
  position: relative;
  z-index: 1;
  border: 1rpx solid rgba(76, 175, 80, 0.15);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.filter-btn.active {
  color: #fff;
  background: linear-gradient(135deg, #43a047, #2e7d32);
  font-weight: 500;
  box-shadow: 0 4rpx 8rpx rgba(46, 125, 50, 0.2);
  border: none;
}

.filter-btn text {
  margin-right: 8rpx;
}

.filter-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

/* 筛选面板 */
.filter-panel {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  width: 80% !important;
  height: 100vh !important; /* 使用视口高度 */
  background-color: #fff !important;
  z-index: 100000 !important; /* 进一步提高z-index，确保高于所有元素 */
  box-shadow: -4rpx 0 20rpx rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
  transform: translateX(100%) !important;
  display: flex !important;
  flex-direction: column !important;
  padding-top: 88px !important; /* 导航栏高度 */
  padding-bottom: 30rpx !important; /* 减少底部padding，因为不再有底部按钮 */
  box-sizing: border-box !important; /* 确保padding不会增加元素总高度 */
}

.filter-panel.show {
  transform: translateX(0) !important;
  right: 0 !important;
}

.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99999; /* 提高遮罩层z-index，但保持低于筛选面板 */
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  pointer-events: none;
}

.filter-mask.show {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.filter-content {
  flex: 1;
  padding: 20rpx; /* 减少内边距 */
  overflow-y: auto; /* 允许内容滚动 */
  padding-bottom: 20rpx; /* 减少底部内边距 */
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

/* 筛选头部样式 */
.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 18rpx;
  border-bottom: 1rpx solid #eee;
}

.filter-title {
  font-size: 32rpx; /* 增大字体大小 */
  font-weight: bold;
  color: #333;
}

.filter-header-actions {
  display: flex;
  align-items: center;
}

.header-reset-btn,
.header-confirm-btn {
  padding: 12rpx 30rpx;
  font-size: 28rpx;
  border-radius: 30rpx;
  margin-left: 18rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.header-reset-btn {
  color: #fff;
  background-color: #ff4d4f;
  border: 1rpx solid #ff4d4f;
}

.header-confirm-btn {
  color: #fff;
  background-color: #07c160;
}

.filter-section {
  margin-bottom: 20rpx; /* 减少底部间距 */
}

.section-title {
  font-size: 26rpx; /* 减小字体大小 */
  color: #333;
  margin-bottom: 15rpx; /* 增加底部间距 */
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rpx; /* 添加左右内边距 */
  cursor: pointer; /* 添加指针样式 */
}

.section-title text {
  padding-right: 10rpx; /* 添加右侧内边距，与切换按钮保持距离 */
}

.section-controls {
  display: flex;
  align-items: center;
  gap: 10rpx; /* 控制元素之间的间距 */
}

.toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: rgba(7, 193, 96, 0.1);
  transition: all 0.3s ease;
}

.toggle-icon:active {
  transform: scale(0.9);
  background-color: rgba(7, 193, 96, 0.2);
}

.section-content {
  transition: all 0.3s ease;
  max-height: 200rpx; /* 足够大以容纳内容 */
  overflow: hidden;
}

.section-content.hidden {
  max-height: 0;
  margin-bottom: 0;
  opacity: 0;
}

/* 精确输入模式的容器样式 */
.exact-input-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.exact-input {
  width: 100%;
  height: 64rpx; /* 减小高度 */
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 16rpx; /* 减少内边距 */
  font-size: 24rpx; /* 减小字体大小 */
  color: #333;
  text-align: center;
  display: flex;
  align-items: center;
  max-width: 60%; /* 进一步限制最大宽度，防止溢出屏幕 */
  margin: 0 auto; /* 居中显示 */
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1); /* 添加轻微阴影 */
  justify-content: center; /* 确保内容居中 */
}

.exact-input-field {
  flex: 1;
  height: 64rpx; /* 减小高度 */
  font-size: 24rpx; /* 减小字体大小 */
  color: #333;
  text-align: center;
  width: 65%; /* 控制输入框宽度 */
  min-width: 0; /* 允许输入框收缩 */
}

.price-input,
.range-input-field {
  width: 160rpx; /* 减小宽度 */
  height: 64rpx; /* 减小高度 */
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 16rpx; /* 减少内边距 */
  font-size: 24rpx; /* 减小字体大小 */
  color: #333;
  text-align: center;
}

.range-separator {
  font-size: 26rpx; /* 减小字体大小 */
  color: #999;
  margin: 0 12rpx; /* 减少间距 */
}

.unit {
  font-size: 24rpx; /* 减小字体大小 */
  color: #666;
  margin-left: 12rpx; /* 减少左边距 */
  white-space: nowrap; /* 防止单位换行 */
  flex-shrink: 0; /* 防止单位被压缩 */
  display: inline-block; /* 确保单位正确显示 */
  padding-right: 6rpx; /* 添加右边距 */
}

.quality-options, .category-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx; /* 减少间距 */
}

.quality-option, .category-option {
  padding: 8rpx 24rpx; /* 减少内边距 */
  font-size: 24rpx; /* 减小字体大小 */
  color: #666;
  background-color: #f2f2f2;
  border-radius: 24rpx; /* 减小圆角 */
  transition: all 0.3s ease;
}

.quality-option.active, .category-option.active {
  color: #fff;
  background-color: #07c160;
  font-weight: 500;
}

/* 添加按钮悬浮效果 */
.button-hover {
  opacity: 0.85;
  transform: scale(0.95);
  transition: all 0.15s ease;
}

/* 为重置和确认按钮添加特定的悬浮效果 */
.header-reset-btn.button-hover {
  background-color: #ff3333;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.header-confirm-btn.button-hover {
  background-color: #06b054;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

/* 确保tabbar正确显示 */
.custom-tab-bar {
  display: flex !important;
  visibility: visible !important;
  z-index: 999 !important;
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  background-color: #ffffff !important;
}

/* 添加供应列表内部容器样式 */
.supply-list-container {
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden; /* 禁止水平滚动 */
  display: flex;
  flex-direction: column;
  align-items: stretch; /* 改为stretch，让列表项填满宽度 */
  padding: 0; /* 移除内边距 */
}

.supply-item {
  margin-bottom: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12rpx;
  overflow: hidden; /* 防止内容溢出 */
  box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.15), 0 0 20rpx rgba(7, 193, 96, 0.1); /* 增强阴影效果，添加发光效果 */
  transition: all 0.3s ease;
  border: 1rpx solid rgba(76, 175, 80, 0.2);
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%; /* 修改为100%填满整个宽度 */
  margin-left: 0; /* 移除左边距 */
  margin-right: 0; /* 移除右边距 */
  box-sizing: border-box; /* 确保边框和内边距不会增加元素总宽度 */
  max-width: 100%; /* 确保不会超出父容器 */
  border-radius: 0; /* 移除圆角，使其填满整个宽度 */
}

.supply-item::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, transparent 50%, rgba(76, 175, 80, 0.1) 50%);
  border-radius: 0 0 0 60rpx;
  z-index: 1;
}

.supply-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.1), 0 0 10rpx rgba(7, 193, 96, 0.05); /* 点击时减弱阴影和发光效果 */
}

/* 修改供应项底部样式，适应移动后的用户标签容器 */
.supply-item-footer {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 15rpx; /* 减少内边距 */
  border-top: 1rpx dashed rgba(0, 0, 0, 0.1);
  background-color: rgba(7, 193, 96, 0.02);
}

/* 用户信息和标签的容器 - 调整在footer中的样式 */
.user-tag-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
}

/* 用户信息和标签布局 */
.supply-item-left,
.supply-item-right {
  display: flex;
  align-items: center;
}

/* 左侧用户信息 */
.supply-item-left {
  flex: 1;
}

/* 右侧标签信息 */
.supply-item-right {
  display: flex;
  gap: 10rpx;
  flex-wrap: wrap;
  justify-content: flex-end;
  max-width: 70%; /* 防止挤压左侧内容 */
}

.user-info {
  flex: 1;
}

.nickname {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.publish-time {
  font-size: 24rpx;
  color: #07c160;
  margin-top: 4rpx;
}

.category {
  font-size: 24rpx;
  color: #07c160;
  background-color: rgba(7, 193, 96, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  max-width: 120rpx; /* 最大宽度限制 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.distance {
  font-size: 22rpx;
  color: #ff6b00;
  background-color: rgba(255, 107, 0, 0.1);
  padding: 6rpx 15rpx;
  border-radius: 20rpx;
  min-width: 100rpx; /* 改为最小宽度 */
  max-width: fit-content; /* 使用 fit-content 让背景适应内容 */
  overflow: hidden; /* 改为 hidden 避免内容溢出 */
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block; /* 使用 inline-block 让元素宽度由内容决定 */
  text-align: center; /* 文字居中显示 */
}

/* 退林还耕容器样式 */
.return-container {
  display: flex;
  justify-content: flex-end;
  padding: 10rpx 20rpx;
  background-color: rgba(76, 175, 80, 0.05);
  border-top: 1rpx dashed rgba(76, 175, 80, 0.3);
  width: 100%;
  box-sizing: border-box;
}

/* 添加退林还耕标签包装器 */
.return-tag-wrapper {
  position: relative;
  padding: 0 10rpx;
}

/* 退林还耕标签样式 */
.return-tag {
  font-size: 28rpx;
  color: #fff;
  background: linear-gradient(135deg, #FF5722, #F44336);
  padding: 8rpx 25rpx;
  border-radius: 22rpx;
  min-width: 140rpx;
  max-width: fit-content;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  text-align: center;
  font-weight: bold;
  box-shadow: 0 3rpx 10rpx rgba(244, 67, 54, 0.3);
  position: relative;
  z-index: 2;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  letter-spacing: 2rpx;
}

/* 添加箭头装饰 */
.return-tag::before {
  content: "◆";
  font-size: 16rpx;
  position: absolute;
  left: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 0 3rpx rgba(255, 87, 34, 0.5);
}

/* 图片和内容布局容器 */
.image-content-container {
  display: flex;
  flex-direction: row;
  gap: 10rpx;
  width: 100%;
  box-sizing: border-box;
  padding: 5rpx 10rpx; /* 调整内边距 */
  overflow: hidden; /* 防止内容溢出 */
}

/* 内容和规格容器 */
.content-specs-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-left: 1rpx;
  width: calc(100% - 260rpx); /* 减去图片宽度和间距 */
  box-sizing: border-box;
  overflow: hidden; /* 确保内容不会溢出 */
}

/* 内容区域样式 */
.content-container {
  display: flex;
  margin-top: 15rpx;
  margin-bottom: 10rpx;
  padding: 8rpx;
  align-items: flex-start;
  background-color: rgba(7, 193, 96, 0.05);
  border-radius: 6rpx;
  width: 100%;
  box-sizing: border-box;
  flex-wrap: wrap; /* 允许内容换行 */
  grid-column: 1 / -1; /* 跨越所有列 */
  border: 1rpx dashed rgba(7, 193, 96, 0.2);
  max-height: 120rpx; /* 限制最大高度 */
  overflow: hidden; /* 隐藏溢出内容 */
}

.content-wrapper {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
}

.content-label {
  font-size: 23rpx;
  color: #07c160;
  font-weight: bold;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.content {
  font-size: 23rpx;
  color: #333;
  line-height: 1.6;
  flex: 1 1 100%; /* 修改为占满整行 */
  text-indent: 1em; /* 首行缩进 */
  white-space: normal; /* 允许文本换行 */
  word-break: break-all; /* 在任意字符间断行 */
  word-wrap: break-word; /* 允许长单词换行 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 添加省略号 */
  display: -webkit-box;
  -webkit-line-clamp: 3; /* 限制最多显示3行 */
  -webkit-box-orient: vertical;
  min-width: 0; /* 确保flex项可以收缩 */
  margin-top: 6rpx; /* 与标签保持一定距离 */
  max-height: 110rpx; /* 限制最大高度 */
}

/* 预览图样式 - 调整尺寸并防止溢出 */
.preview-image {
  width: 270rpx;
  height: 270rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
  border: 1rpx solid rgba(7, 193, 96, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  object-fit: cover;
  transition: all 0.3s ease;
  position: relative;
  margin-left: -15rpx; /* 向左偏移 */
  max-width: 100%; /* 确保图片不会超出容器 */
}

.preview-image:active {
  transform: scale(0.97);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

/* 规格信息样式 */
.specs-info {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 8rpx;
  align-content: start;
  padding: 8rpx 5rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  margin-top: 10rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden; /* 确保内容不会溢出 */
}

.spec-item {
  font-size: 24rpx;
  display: flex;
  align-items: center;
  background-color: rgba(7, 193, 96, 0.03);
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  box-sizing: border-box;
}

.spec-label {
  color: #666;
  margin-right: 6rpx;
  flex-shrink: 0;
}

.spec-value {
  color: #333;
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 质量值的特殊样式 */
.quality-value {
  color: #ff6b00 !important; /* 橙色 */
  font-weight: bold !important;
}

/* 价格标签和值的样式 */
.price-label {
  font-size: 27rpx;
  color: #666;
  margin-right: 8rpx;
}

.price-value {
  font-size: 29rpx;
  color: #ff4d4f;
  font-weight: bold;
}

.actions {
  display: flex;
  align-items: center;
}

.share-btn {
  /* 已删除，保留样式以免影响其他地方引用 */
  display: none;
}

.share-btn::after {
  /* 已删除，保留样式以免影响其他地方引用 */
  display: none;
}

.share-btn text {
  /* 已删除，保留样式以免影响其他地方引用 */
  display: none;
}

.stats {
  display: flex;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  color: #999;
  font-size: 24rpx;
}

.icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 6rpx;
}

/* 状态提示样式 */
.empty-state, .loading, .load-more, .no-more {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  padding: 20rpx 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
  margin-left: 10rpx;
}

/* 悬浮按钮 - 调整位置以适应tabbar */
.publish-btn, .back-top {
  position: fixed;
  right: 40rpx;
  z-index: 999; /* 确保悬浮按钮的z-index低于筛选面板 */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  pointer-events: auto; /* 确保按钮可点击 */
  margin-bottom: 160rpx; /* 增加底部margin，避免与tabbar重叠 */
}

/* 当筛选面板打开时，隐藏悬浮按钮 - 使用更强的选择器 */
.container .filter-panel.show ~ .publish-btn,
.container .filter-panel.show ~ .back-top,
.filter-mask.show ~ .publish-btn,
.filter-mask.show ~ .back-top {
  opacity: 0;
  pointer-events: none; /* 禁用点击 */
  z-index: -1; /* 将z-index设为负值，确保在所有元素下方 */
}

/* 确保筛选面板显示时，悬浮按钮不可见 */
.showFilter .publish-btn,
.showFilter .back-top {
  display: none !important;
}

/* 发布按钮 */
.publish-btn {
  bottom: 31rpx;
  width: 80rpx; /* 增加宽度适应横向显示 */
  height: 80rpx;
  background: linear-gradient(135deg, #07c160, #00b050);
  border-radius: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(7, 193, 96, 0.4);
  padding: 0 20rpx;
  display: flex;
  flex-direction: row; /* 确保内容横向排列 */
  align-items: center;
  justify-content: center;
}

.publish-btn:active {
  transform: translateY(4rpx) scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(7, 193, 96, 0.2);
}

.publish-btn text {
  font-size: 17rpx;
  writing-mode: horizontal-tb !important; /* 强制横向显示 */
  display: inline-block; /* 确保横向显示 */
  transform: rotate(0deg); /* 确保不旋转 */
  color: #ebf18a; /* 修改为白色，与图标统一 */
  font-weight: 700;
  letter-spacing: 1rpx;
  margin-left: -8rpx;
  white-space: nowrap; /* 禁止文字换行 */
  vertical-align: middle; /* 垂直居中 */
}

/* 回到顶部 */
.back-top {
  bottom: 112rpx;
  width: 64rpx;
  height: 64rpx;
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
}

.back-top:active {
  transform: translateY(2rpx) scale(0.95);
  box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* t-design 风格卡片 */
.card-shadow {
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
  border-radius: 12rpx;
  background-color: #fff;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 数量信息样式 */
.quantity-info {
  display: flex;
  align-items: center;
  padding: 6rpx 14rpx;
  background-color: rgba(255, 107, 0, 0.1);
  border-radius: 8rpx;
  margin-left: 16rpx;
  flex-shrink: 0;
}

.quantity-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 6rpx;
  font-weight: bold;
}

.quantity-value {
  font-size: 28rpx;
  color: #ff6b00;
  font-weight: bold;
}

/* 当筛选面板打开时，固定容器防止滚动 */
.container.showFilter {
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* 详细描述顶格效果 */
.description-top {
  text-indent: 0;
  margin-top: 0;
  padding-top: 0;
}

.mode-switch {
  display: flex;
  align-items: center;
  font-size: 24rpx; /* 增大字体大小 */
  color: #07c160;
  background-color: rgba(7, 193, 96, 0.1);
  padding: 8rpx 16rpx; /* 增大内边距 */
  border-radius: 20rpx; /* 增大圆角 */
  transition: all 0.3s ease;
  font-weight: 500; /* 增加字体粗细 */
  box-shadow: 0 1rpx 3rpx rgba(7, 193, 96, 0.1); /* 添加轻微阴影 */
}

.mode-switch:active,
.mode-switch-hover {
  opacity: 0.7;
  transform: scale(0.95);
  background-color: rgba(7, 193, 96, 0.2);
}

.range-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 供应项容器 - 替代原来的 header */
.supply-item-container {
  display: flex;
  flex-direction: column;
  padding: 3px 10px; /* 调整内边距，减少上下间距，保留左右间距 */
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  background: linear-gradient(to right, rgba(7, 193, 96, 0.03), rgba(7, 193, 96, 0.05));
}

/* 主要内容区域 */
.supply-item-content {
  padding: 10rpx 15rpx; /* 减少上下内边距 */
  display: flex;
  flex-direction: column;
  flex: 1;
  background: #ffffff; /* 改为纯白色背景 */
  box-sizing: border-box;
  width: 100%;
}

/* 标题区域样式 */
.title-section {
  display: flex;
  flex-direction: column;
  margin-top: 16rpx;
  width: 100%;
  clear: both;
  padding-top: 10rpx;
  border-top: 1rpx dashed rgba(7, 193, 96, 0.1);
}

/* 标题卡片样式 */
.title-card {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 0rpx;
  padding: 10rpx 16rpx;
  background-color: rgba(7, 193, 96, 0.08);
  border-radius: 10rpx;
  border-left: 6rpx solid #07c160;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap; /* 修改为wrap，允许在空间不足时换行 */
  width: 100%; /* 确保宽度是100% */
  box-sizing: border-box; /* 确保padding不会导致宽度溢出 */
}

.title-text {
  margin-left:0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 34rpx;
  display: flex;
  align-items: center;
  min-width: 0; /* 确保flex项可以收缩到比内容更小 */
  max-width: 100%; /* 最大宽度100% */
}

/* 添加标题卡片中的价格信息样式 */
.title-price-info {
  display: flex;
  flex-direction: row;
  margin-right: 0rpx;
  gap: 10rpx; /* 减小间距 */
  flex-shrink: 0; /* 不允许收缩 */
  margin-left: 10rpx; /* 增加左边距，与标题保持距离 */
}

.title-price-item {
  display: flex;
  align-items: center;
  margin-bottom: 4rpx;
  white-space: nowrap; /* 保持在一行 */
}

.quantity-info {
  display: flex;
  align-items: center;
  margin-left: 10rpx;
  white-space: nowrap;
}

.supply-prefix {
  font-weight: bold;
  color: #333333;
  font-size: 32rpx;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.supply-content {
  background: linear-gradient(135deg, #cc1e1e, #c70303);
  -webkit-background-clip: text;
  color: transparent;
  font-size: 35rpx;
  font-weight: 600;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 2rpx 0;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
  letter-spacing: 1.5rpx;
  min-width: 0; /* 确保flex项可以收缩 */
}

/* 用户信息和标签的容器 - 调整在footer中的样式 */
.user-tag-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
}

/* 用户信息和标签布局 */
.supply-item-left,
.supply-item-right {
  display: flex;
  align-items: center;
}

/* 左侧用户信息 */
.supply-item-left {
  flex: 1;
}

/* 右侧标签信息 */
.supply-item-right {
  display: flex;
  gap: 10rpx;
  flex-shrink: 0; /* 不允许收缩 */
  flex-wrap: wrap; /* 允许在空间不足时换行 */
  justify-content: flex-end; /* 右对齐 */
}

/* 新增的精确筛选容器样式 */
.quick-filter-bar {
  width: 100%;
  background-color: #f8f8f8;
  border-bottom: 1rpx solid rgba(76, 175, 80, 0.2);
  padding: 10rpx 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 10rpx;
}

.quick-filter-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 改为3列布局 */
  gap: 6rpx;
  width: 100%;
  padding: 0 4rpx;
}

.quick-filter-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: 6rpx;
  padding: 4rpx 8rpx;
 
  
}

.quick-filter-label {
  font-size: 22rpx;
  color: #07c160;
  font-weight: 500;
  min-width: 36rpx;
  margin-right: 4rpx;
  white-space: nowrap;
  flex-shrink: 0;
}

.quick-filter-input {
  width: 93rpx;
 
  height: 40rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  text-align: center;
  font-size: 20rpx;
  color: #333;
  border: 1rpx solid rgba(76, 175, 80, 0.2);
  padding: 0;
  margin: 0 4rpx;
  flex-grow: 0;
}

.quick-filter-unit {
  font-size: 26rpx;
  color: #666;
  min-width: 20rpx;
  text-align: left;
  flex-shrink: 0;
}

/* 植物名称推荐列表样式 */
.plant-suggestions-container {
  position: fixed;  /* 固定定位 */
  top: calc(88px + 110rpx);  /* 导航栏高度 + 搜索栏高度 */
  left: 30rpx;
  width: calc(100% - 60rpx);
  max-height: 70vh; /* 使用视口高度的70%作为最大高度 */
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  overflow: visible;
  z-index: 10001; /* 确保推荐列表位于最上层，高于所有其他元素 */
  border: 1rpx solid #d9e6df;
  display: flex;
  flex-direction: column;
}

.plant-suggestions-container::before {
  content: '';
  position: absolute;
  top: -10rpx;
  left: 60rpx; /* 调整小三角的水平位置 */
  width: 20rpx;
  height: 20rpx;
  background-color: #fff;
  transform: rotate(45deg);
  border-left: 1rpx solid #d9e6df;
  border-top: 1rpx solid #d9e6df;
  z-index: 1;
  box-shadow: -2rpx -2rpx 5rpx rgba(0, 0, 0, 0.05); /* 添加阴影效果 */
}

.suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
  border-bottom: 1rpx solid #e8f5ee;
  background: linear-gradient(to right, #e8f5ee, #f0f9f4);
  flex-shrink: 0;
}

.suggestions-title {
  font-size: 26rpx;
  color: #07c160;
  font-weight: 500;
}

.suggestions-close {
  font-size: 32rpx;
  color: #999;
  font-weight: bold;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f0f0f0;
  transition: all 0.2s;
}

.suggestions-close:active {
  background-color: #e0e0e0;
  transform: scale(0.95);
}

.suggestions-scroll {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
  padding: 10rpx;
  flex: 1;
  text-align: center; /* 使内容居中 */
}

.suggestions-scroll-inner {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: flex-start; /* 确保项目从左开始排列 */
  align-content: flex-start; /* 确保行从顶部开始排列 */
}

/* 三列布局的植物推荐列表 */
.suggestion-item {
  display: flex; /* 改为flex布局 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  width: calc(33.33% - 10rpx);
  margin: 5rpx;
  padding: 12rpx 8rpx; /* 增加左右内边距 */
  box-sizing: border-box;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  transition: all 0.2s;
  border: 1rpx solid #e8f5ee;
  flex-shrink: 0; /* 防止项目收缩 */
  min-height: 60rpx; /* 设置最小高度，确保一致性 */
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item-hover {
  background-color: #e8f5ee;
  transform: scale(0.98);
}

.suggestion-name {
  font-size: 25rpx; /* 稍微减小字体，为换行留出空间 */
  color: #333;
  font-weight: 500; /* 增加字重 */
  white-space: normal; /* 允许正常换行 */
  word-break: break-word; /* 在单词边界换行，避免强制断词 */
  line-height: 1.3; /* 设置行高，让多行文本更美观 */
  width: 100%; /* 确保占满容器宽度 */
  text-align: center; /* 文本居中 */
  max-height: 3.9em; /* 限制最大高度为3行 */
  overflow: hidden; /* 超出部分隐藏 */
  display: -webkit-box; /* 使用webkit-box布局 */
  -webkit-line-clamp: 3; /* 最多显示3行 */
  -webkit-box-orient: vertical; /* 垂直方向 */
  padding: 0 5rpx;
  line-height: 1.4; /* 增加行高 */
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; /* 指定字体 */
  text-shadow: 0 0.5px 0 rgba(255, 255, 255, 0.8); /* 轻微文字阴影提高可读性 */
}